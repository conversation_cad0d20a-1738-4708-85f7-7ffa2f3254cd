version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14
    container_name: mvp_postgres
    environment:
      POSTGRES_DB: voiceagent_db
      POSTGRES_USER: voiceagent
      POSTGRES_PASSWORD: voiceagent_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - mvp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U voiceagent -d voiceagent_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: mvp_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mvp_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Vector Database for Semantic Search
  qdrant:
    image: qdrant/qdrant:latest
    container_name: mvp_qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - mvp_network
    healthcheck:
      test: ["CMD", "test", "-f", "/qdrant/storage/raft_state.json"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # MinIO for File Storage
  minio:
    image: minio/minio:latest
    container_name: mvp_minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - mvp_network
    restart: unless-stopped

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mvp_backend
    environment:
      - DATABASE_URL=*********************************************************/voiceagent_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
      - OPENAI_API_KEY=${OPENAI_API_KEY:-sk-demo-key-for-mvp}
      - TWILIO_ACCOUNT_SID=demo-account-sid
      - TWILIO_AUTH_TOKEN=demo-auth-token
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mvp_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped


  # Data Integration Service
  data-integration:
    build:
      context: ./data-integration-service
      dockerfile: Dockerfile
    container_name: mvp_data_integration
    environment:
      - DATABASE_URL=*********************************************************/voiceagent_db
      - REDIS_URL=redis://redis:6379
      - VECTOR_DB_URL=http://qdrant:6333
      - OPENAI_API_KEY=${OPENAI_API_KEY:-sk-demo-key-for-mvp}
      - MCP_SERVER_PORT=8002
      - FILE_STORAGE_PATH=/app/storage
      - MAX_FILE_SIZE=*********
      - SUPPORTED_FORMATS=["xlsx","csv","json","pdf","txt","docx"]
    ports:
      - "8001:8001"  # Main API
      - "8002:8002"  # MCP Server
      - "8003:8003"  # WebSocket
    volumes:
      - ./data-integration-service/storage:/app/storage
      - ./data-integration-service:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    networks:
      - mvp_network
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mvp_frontend
    environment:
      - NODE_OPTIONS=--max-old-space-size=1024
      - REACT_APP_API_URL=http://localhost:8000/api/v1
      - REACT_APP_DATA_INTEGRATION_API_URL=http://localhost:8001/api/v1
      - REACT_APP_ENVIRONMENT=development
      - CHOKIDAR_USEPOLLING=true
      - CHOKIDAR_INTERVAL=1000
      - WATCHPACK_POLLING=true
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./frontend/package.json:/app/package.json
      - ./frontend/tsconfig.json:/app/tsconfig.json
    depends_on:
      - backend
      - data-integration
    networks:
      - mvp_network
    restart: unless-stopped
    mem_limit: 2g
    mem_reservation: 1g

  # Adminer for Database Management (Development only)
  adminer:
    image: adminer
    container_name: mvp_adminer
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres
    networks:
      - mvp_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local
  minio_data:
    driver: local

networks:
  mvp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
